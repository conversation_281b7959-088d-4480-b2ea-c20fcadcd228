'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import NovelSelector from '@/components/NovelSelector';
import ChapterSelector from '@/components/ChapterSelector';
import RuleEditor from '@/components/RuleEditor';
import RewriteProgress from '@/components/RewriteProgress';
import { Novel, Chapter } from '@/lib/database';
import { HelpCircle } from 'lucide-react';

export default function Home() {
  const [selectedNovel, setSelectedNovel] = useState<Novel | null>(null);
  const [selectedChapters, setSelectedChapters] = useState<string>('');
  const [rewriteRules, setRewriteRules] = useState<string>('');
  const [isRewriting, setIsRewriting] = useState(false);
  const [currentJobId, setCurrentJobId] = useState<string | null>(null);

  const handleStartRewrite = async () => {
    if (!selectedNovel || !selectedChapters || !rewriteRules) {
      alert('请完整填写所有信息');
      return;
    }

    setIsRewriting(true);

    try {
      const response = await fetch('/api/rewrite', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          novelId: selectedNovel.id,
          chapterRange: selectedChapters,
          rules: rewriteRules,
        }),
      });

      const result = await response.json();

      if (result.success) {
        setCurrentJobId(result.data.jobId);
      } else {
        alert(`改写失败: ${result.error}`);
        setIsRewriting(false);
      }
    } catch (error) {
      console.error('改写请求失败:', error);
      alert('改写请求失败');
      setIsRewriting(false);
    }
  };

  const handleRewriteComplete = () => {
    setIsRewriting(false);
    setCurrentJobId(null);
    alert('改写完成！');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center justify-between mb-8">
          <h1 className="text-3xl font-bold text-gray-800">
            小说改写工具
          </h1>
          <Link
            href="/help"
            className="flex items-center px-4 py-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
          >
            <HelpCircle className="mr-2" size={20} />
            使用帮助
          </Link>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* 左侧：改写规则 */}
          <div className="lg:col-span-1">
            <RuleEditor
              rules={rewriteRules}
              onRulesChange={setRewriteRules}
              disabled={isRewriting}
            />
          </div>

          {/* 中间：小说选择 */}
          <div className="lg:col-span-1">
            <NovelSelector
              selectedNovel={selectedNovel}
              onNovelSelect={setSelectedNovel}
              disabled={isRewriting}
            />
          </div>

          {/* 右侧：章节选择 */}
          <div className="lg:col-span-1">
            <ChapterSelector
              novel={selectedNovel}
              selectedChapters={selectedChapters}
              onChaptersChange={setSelectedChapters}
              disabled={isRewriting}
            />
          </div>
        </div>

        {/* 操作按钮 */}
        <div className="mt-8 text-center">
          <button
            onClick={handleStartRewrite}
            disabled={isRewriting || !selectedNovel || !selectedChapters || !rewriteRules}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-bold py-3 px-8 rounded-lg transition-colors"
          >
            {isRewriting ? '改写中...' : '开始改写'}
          </button>
        </div>

        {/* 进度显示 */}
        {isRewriting && currentJobId && (
          <div className="mt-8">
            <RewriteProgress
              jobId={currentJobId}
              onComplete={handleRewriteComplete}
            />
          </div>
        )}
      </div>
    </div>
  );
}
