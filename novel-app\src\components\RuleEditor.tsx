'use client';

import { useState } from 'react';
import { Settings, HelpCircle, Wand2 } from 'lucide-react';
import { PRESET_RULES } from '@/lib/gemini';

interface RuleEditorProps {
  rules: string;
  onRulesChange: (rules: string) => void;
  disabled?: boolean;
}

export default function RuleEditor({ rules, onRulesChange, disabled }: RuleEditorProps) {
  const [showPresets, setShowPresets] = useState(false);
  const [showHelp, setShowHelp] = useState(false);

  const handlePresetSelect = (presetKey: string) => {
    const preset = PRESET_RULES[presetKey as keyof typeof PRESET_RULES];
    if (preset) {
      onRulesChange(preset.rules);
      setShowPresets(false);
    }
  };

  const presetButtons = Object.entries(PRESET_RULES).filter(([key]) => key !== 'custom');

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-xl font-semibold text-gray-800 flex items-center">
          <Settings className="mr-2" size={20} />
          改写规则
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={() => setShowHelp(!showHelp)}
            className="p-2 text-gray-600 hover:text-gray-800"
            title="帮助"
          >
            <HelpCircle size={16} />
          </button>
          <button
            onClick={() => setShowPresets(!showPresets)}
            disabled={disabled}
            className="p-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
            title="预设规则"
          >
            <Wand2 size={16} />
          </button>
        </div>
      </div>

      {/* 帮助信息 */}
      {showHelp && (
        <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <h3 className="font-medium text-blue-800 mb-2">改写规则说明</h3>
          <div className="text-sm text-blue-700 space-y-2">
            <p>• <strong>感情戏增强:</strong> 扩写男女主互动，简化战斗等非感情戏</p>
            <p>• <strong>人设修正:</strong> 优化主角性格和对话风格</p>
            <p>• <strong>毒点清除:</strong> 移除送女、绿帽等不适内容</p>
            <p>• <strong>节奏优化:</strong> 删除拖沓内容，加快故事节奏</p>
            <p>• <strong>自定义规则:</strong> 可以详细描述你想要的改写方向</p>
          </div>
        </div>
      )}

      {/* 预设规则 */}
      {showPresets && (
        <div className="mb-4 p-4 bg-gray-50 border border-gray-200 rounded-lg">
          <h3 className="font-medium text-gray-800 mb-3">选择预设规则</h3>
          <div className="grid grid-cols-1 gap-2">
            {presetButtons.map(([key, preset]) => (
              <button
                key={key}
                onClick={() => handlePresetSelect(key)}
                disabled={disabled}
                className="text-left p-3 border border-gray-200 rounded hover:border-blue-300 hover:bg-blue-50 disabled:opacity-50 transition-colors"
              >
                <div className="font-medium text-gray-800">{preset.name}</div>
                <div className="text-sm text-gray-600">{preset.description}</div>
              </button>
            ))}
          </div>
        </div>
      )}

      {/* 规则编辑器 */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">
          改写规则内容
        </label>
        <textarea
          value={rules}
          onChange={(e) => onRulesChange(e.target.value)}
          disabled={disabled}
          placeholder="请输入详细的改写规则，例如：&#10;&#10;1. 扩写男女主角之间的互动情节&#10;2. 对战斗场面一笔带过&#10;3. 增加情感描写和心理活动&#10;4. 修改不合理的人物行为&#10;..."
          className="w-full h-64 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none disabled:bg-gray-100"
        />
        <div className="mt-2 text-xs text-gray-500">
          {rules.length} 字符 • 建议详细描述改写要求以获得更好的效果
        </div>
      </div>

      {/* 规则示例 */}
      {!rules && (
        <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
          <h3 className="font-medium text-yellow-800 mb-2">规则示例</h3>
          <div className="text-sm text-yellow-700 space-y-1">
            <p>• 在不改变小说故事走向的情况下，扩写男女主互动内容</p>
            <p>• 对非感情戏部分（例如战斗）一笔带过</p>
            <p>• 修改主角人设，使其更加讨喜</p>
            <p>• 删除送女、绿帽等毒点情节</p>
            <p>• 优化对话风格，使其更加自然</p>
          </div>
        </div>
      )}

      {/* 规则验证 */}
      {rules && (
        <div className="mt-4">
          {rules.length < 20 ? (
            <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
              <div className="text-sm text-red-700">
                ⚠️ 规则内容过于简单，建议添加更详细的描述以获得更好的改写效果
              </div>
            </div>
          ) : rules.length > 2000 ? (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
              <div className="text-sm text-yellow-700">
                ⚠️ 规则内容较长，可能影响AI理解，建议精简到重点内容
              </div>
            </div>
          ) : (
            <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
              <div className="text-sm text-green-700">
                ✓ 规则内容长度适中，应该能获得良好的改写效果
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
