'use client';

import { useState, useEffect } from 'react';
import { CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';

interface RewriteProgressProps {
  jobId: string;
  onComplete: () => void;
}

interface JobStatus {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: string;
  createdAt: string;
  updatedAt: string;
}

export default function RewriteProgress({ jobId, onComplete }: RewriteProgressProps) {
  const [job, setJob] = useState<JobStatus | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const interval = setInterval(checkJobStatus, 2000); // 每2秒检查一次状态
    checkJobStatus(); // 立即检查一次

    return () => clearInterval(interval);
  }, [jobId]);

  const checkJobStatus = async () => {
    try {
      const response = await fetch(`/api/jobs?jobId=${jobId}`);
      const result = await response.json();
      
      if (result.success) {
        setJob(result.data);
        setLoading(false);
        
        // 如果任务完成或失败，停止轮询并通知父组件
        if (result.data.status === 'completed' || result.data.status === 'failed') {
          setTimeout(() => {
            onComplete();
          }, 2000); // 2秒后通知完成
        }
      } else {
        console.error('获取任务状态失败:', result.error);
      }
    } catch (error) {
      console.error('获取任务状态失败:', error);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending':
        return <Clock className="text-yellow-500" size={20} />;
      case 'processing':
        return <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>;
      case 'completed':
        return <CheckCircle className="text-green-500" size={20} />;
      case 'failed':
        return <XCircle className="text-red-500" size={20} />;
      default:
        return <AlertCircle className="text-gray-500" size={20} />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'pending':
        return '等待处理';
      case 'processing':
        return '正在改写';
      case 'completed':
        return '改写完成';
      case 'failed':
        return '改写失败';
      default:
        return '未知状态';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'processing':
        return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'completed':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'failed':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
          <p className="text-gray-600">获取任务状态中...</p>
        </div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="bg-white rounded-lg shadow-md p-6">
        <div className="text-center py-4 text-red-600">
          <XCircle className="mx-auto mb-2" size={32} />
          <p>无法获取任务状态</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h3 className="text-lg font-semibold text-gray-800 mb-4">改写进度</h3>
      
      {/* 状态显示 */}
      <div className={`p-4 rounded-lg border ${getStatusColor(job.status)} mb-4`}>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            {getStatusIcon(job.status)}
            <span className="ml-2 font-medium">{getStatusText(job.status)}</span>
          </div>
          <span className="text-sm">
            {job.progress}%
          </span>
        </div>
      </div>

      {/* 进度条 */}
      <div className="mb-4">
        <div className="flex justify-between text-sm text-gray-600 mb-1">
          <span>进度</span>
          <span>{job.progress}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${
              job.status === 'completed'
                ? 'bg-green-500'
                : job.status === 'failed'
                ? 'bg-red-500'
                : 'bg-blue-500'
            }`}
            style={{ width: `${job.progress}%` }}
          ></div>
        </div>
      </div>

      {/* 时间信息 */}
      <div className="text-sm text-gray-500 space-y-1">
        <div>开始时间: {new Date(job.createdAt).toLocaleString()}</div>
        <div>更新时间: {new Date(job.updatedAt).toLocaleString()}</div>
      </div>

      {/* 结果信息 */}
      {job.result && (
        <div className="mt-4 p-3 bg-gray-50 border border-gray-200 rounded-lg">
          <h4 className="font-medium text-gray-800 mb-2">结果信息</h4>
          <p className="text-sm text-gray-700 whitespace-pre-wrap">{job.result}</p>
        </div>
      )}

      {/* 操作提示 */}
      {job.status === 'completed' && (
        <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex items-center text-green-700">
            <CheckCircle className="mr-2" size={16} />
            <span className="text-sm">
              改写完成！改写后的文件已保存到 data/rewritten 目录中。
            </span>
          </div>
        </div>
      )}

      {job.status === 'failed' && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <div className="flex items-center text-red-700">
            <XCircle className="mr-2" size={16} />
            <span className="text-sm">
              改写失败，请检查错误信息并重试。
            </span>
          </div>
        </div>
      )}

      {job.status === 'processing' && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center text-blue-700">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
            <span className="text-sm">
              正在使用 Gemini AI 改写章节，请耐心等待...
            </span>
          </div>
        </div>
      )}
    </div>
  );
}
