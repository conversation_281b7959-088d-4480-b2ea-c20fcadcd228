import fs from 'fs';
import path from 'path';

// 数据类型定义
export interface Novel {
  id: string;
  title: string;
  filename: string;
  createdAt: string;
  chapterCount?: number;
}

export interface Chapter {
  id: string;
  novelId: string;
  chapterNumber: number;
  title: string;
  content: string;
  filename: string;
  createdAt: string;
}

export interface RewriteRule {
  id: string;
  name: string;
  description: string;
  rules: string;
  createdAt: string;
  updatedAt: string;
}

export interface RewriteJob {
  id: string;
  novelId: string;
  chapters: number[];
  ruleId: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  progress: number;
  result?: string;
  createdAt: string;
  updatedAt: string;
}

// 数据存储路径
const DATA_DIR = path.join(process.cwd(), 'data');
const NOVELS_FILE = path.join(DATA_DIR, 'novels.json');
const CHAPTERS_FILE = path.join(DATA_DIR, 'chapters.json');
const RULES_FILE = path.join(DATA_DIR, 'rewrite_rules.json');
const JOBS_FILE = path.join(DATA_DIR, 'rewrite_jobs.json');

// 确保数据目录存在
function ensureDataDir() {
  if (!fs.existsSync(DATA_DIR)) {
    fs.mkdirSync(DATA_DIR, { recursive: true });
  }
}

// 读取JSON文件
function readJsonFile<T>(filePath: string): T[] {
  ensureDataDir();
  if (!fs.existsSync(filePath)) {
    return [];
  }
  try {
    const data = fs.readFileSync(filePath, 'utf-8');
    return JSON.parse(data);
  } catch (error) {
    console.error(`Error reading ${filePath}:`, error);
    return [];
  }
}

// 写入JSON文件
function writeJsonFile<T>(filePath: string, data: T[]) {
  ensureDataDir();
  try {
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
  } catch (error) {
    console.error(`Error writing ${filePath}:`, error);
    throw error;
  }
}

// 生成唯一ID
function generateId(): string {
  return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// 小说相关操作
export const novelDb = {
  getAll: (): Novel[] => readJsonFile<Novel>(NOVELS_FILE),
  
  getById: (id: string): Novel | undefined => {
    const novels = readJsonFile<Novel>(NOVELS_FILE);
    return novels.find(novel => novel.id === id);
  },
  
  create: (novel: Omit<Novel, 'id' | 'createdAt'>): Novel => {
    const novels = readJsonFile<Novel>(NOVELS_FILE);
    const newNovel: Novel = {
      ...novel,
      id: generateId(),
      createdAt: new Date().toISOString(),
    };
    novels.push(newNovel);
    writeJsonFile(NOVELS_FILE, novels);
    return newNovel;
  },
  
  update: (id: string, updates: Partial<Novel>): Novel | null => {
    const novels = readJsonFile<Novel>(NOVELS_FILE);
    const index = novels.findIndex(novel => novel.id === id);
    if (index === -1) return null;
    
    novels[index] = { ...novels[index], ...updates };
    writeJsonFile(NOVELS_FILE, novels);
    return novels[index];
  },
  
  delete: (id: string): boolean => {
    const novels = readJsonFile<Novel>(NOVELS_FILE);
    const index = novels.findIndex(novel => novel.id === id);
    if (index === -1) return false;
    
    novels.splice(index, 1);
    writeJsonFile(NOVELS_FILE, novels);
    return true;
  }
};

// 章节相关操作
export const chapterDb = {
  getAll: (): Chapter[] => readJsonFile<Chapter>(CHAPTERS_FILE),
  
  getByNovelId: (novelId: string): Chapter[] => {
    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);
    return chapters.filter(chapter => chapter.novelId === novelId);
  },
  
  getById: (id: string): Chapter | undefined => {
    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);
    return chapters.find(chapter => chapter.id === id);
  },
  
  create: (chapter: Omit<Chapter, 'id' | 'createdAt'>): Chapter => {
    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);
    const newChapter: Chapter = {
      ...chapter,
      id: generateId(),
      createdAt: new Date().toISOString(),
    };
    chapters.push(newChapter);
    writeJsonFile(CHAPTERS_FILE, chapters);
    return newChapter;
  },
  
  createBatch: (chapters: Omit<Chapter, 'id' | 'createdAt'>[]): Chapter[] => {
    const existingChapters = readJsonFile<Chapter>(CHAPTERS_FILE);
    const newChapters = chapters.map(chapter => ({
      ...chapter,
      id: generateId(),
      createdAt: new Date().toISOString(),
    }));
    existingChapters.push(...newChapters);
    writeJsonFile(CHAPTERS_FILE, existingChapters);
    return newChapters;
  },
  
  delete: (id: string): boolean => {
    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);
    const index = chapters.findIndex(chapter => chapter.id === id);
    if (index === -1) return false;
    
    chapters.splice(index, 1);
    writeJsonFile(CHAPTERS_FILE, chapters);
    return true;
  },
  
  deleteByNovelId: (novelId: string): boolean => {
    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);
    const filteredChapters = chapters.filter(chapter => chapter.novelId !== novelId);
    writeJsonFile(CHAPTERS_FILE, filteredChapters);
    return true;
  }
};

// 改写规则相关操作
export const ruleDb = {
  getAll: (): RewriteRule[] => readJsonFile<RewriteRule>(RULES_FILE),
  
  getById: (id: string): RewriteRule | undefined => {
    const rules = readJsonFile<RewriteRule>(RULES_FILE);
    return rules.find(rule => rule.id === id);
  },
  
  create: (rule: Omit<RewriteRule, 'id' | 'createdAt' | 'updatedAt'>): RewriteRule => {
    const rules = readJsonFile<RewriteRule>(RULES_FILE);
    const newRule: RewriteRule = {
      ...rule,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    rules.push(newRule);
    writeJsonFile(RULES_FILE, rules);
    return newRule;
  },
  
  update: (id: string, updates: Partial<RewriteRule>): RewriteRule | null => {
    const rules = readJsonFile<RewriteRule>(RULES_FILE);
    const index = rules.findIndex(rule => rule.id === id);
    if (index === -1) return null;
    
    rules[index] = { 
      ...rules[index], 
      ...updates, 
      updatedAt: new Date().toISOString() 
    };
    writeJsonFile(RULES_FILE, rules);
    return rules[index];
  },
  
  delete: (id: string): boolean => {
    const rules = readJsonFile<RewriteRule>(RULES_FILE);
    const index = rules.findIndex(rule => rule.id === id);
    if (index === -1) return false;
    
    rules.splice(index, 1);
    writeJsonFile(RULES_FILE, rules);
    return true;
  }
};

// 改写任务相关操作
export const jobDb = {
  getAll: (): RewriteJob[] => readJsonFile<RewriteJob>(JOBS_FILE),
  
  getById: (id: string): RewriteJob | undefined => {
    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);
    return jobs.find(job => job.id === id);
  },
  
  create: (job: Omit<RewriteJob, 'id' | 'createdAt' | 'updatedAt'>): RewriteJob => {
    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);
    const newJob: RewriteJob = {
      ...job,
      id: generateId(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    jobs.push(newJob);
    writeJsonFile(JOBS_FILE, jobs);
    return newJob;
  },
  
  update: (id: string, updates: Partial<RewriteJob>): RewriteJob | null => {
    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);
    const index = jobs.findIndex(job => job.id === id);
    if (index === -1) return null;
    
    jobs[index] = { 
      ...jobs[index], 
      ...updates, 
      updatedAt: new Date().toISOString() 
    };
    writeJsonFile(JOBS_FILE, jobs);
    return jobs[index];
  },
  
  delete: (id: string): boolean => {
    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);
    const index = jobs.findIndex(job => job.id === id);
    if (index === -1) return false;
    
    jobs.splice(index, 1);
    writeJsonFile(JOBS_FILE, jobs);
    return true;
  }
};
