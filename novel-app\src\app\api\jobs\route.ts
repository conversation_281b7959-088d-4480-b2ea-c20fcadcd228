import { NextRequest, NextResponse } from 'next/server';
import { jobDb } from '@/lib/database';

// GET - 获取任务列表或单个任务状态
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId');
    
    if (jobId) {
      // 获取单个任务状态
      const job = jobDb.getById(jobId);
      if (!job) {
        return NextResponse.json(
          { success: false, error: '任务不存在' },
          { status: 404 }
        );
      }
      
      return NextResponse.json({
        success: true,
        data: job,
      });
    } else {
      // 获取所有任务
      const jobs = jobDb.getAll();
      return NextResponse.json({
        success: true,
        data: jobs,
      });
    }
  } catch (error) {
    console.error('获取任务信息失败:', error);
    return NextResponse.json(
      { success: false, error: '获取任务信息失败' },
      { status: 500 }
    );
  }
}
