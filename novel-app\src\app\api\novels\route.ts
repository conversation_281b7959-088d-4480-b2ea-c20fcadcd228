import { NextRequest, NextResponse } from 'next/server';
import { novelDb } from '@/lib/database';
import { getAvailableNovels, parseNovelFile, isNovelParsed, reparseNovel } from '@/lib/novel-parser';
import path from 'path';

// GET - 获取所有小说列表
export async function GET() {
  try {
    const novels = novelDb.getAll();
    const availableFiles = getAvailableNovels();
    
    // 标记哪些文件已经被解析
    const novelsWithStatus = availableFiles.map(filename => {
      const parsed = novels.find(novel => novel.filename === filename);
      return {
        filename,
        parsed: !!parsed,
        novel: parsed || null,
      };
    });
    
    return NextResponse.json({
      success: true,
      data: {
        novels: novels,
        availableFiles: novelsWithStatus,
      },
    });
  } catch (error) {
    console.error('获取小说列表失败:', error);
    return NextResponse.json(
      { success: false, error: '获取小说列表失败' },
      { status: 500 }
    );
  }
}

// POST - 解析小说文件
export async function POST(request: NextRequest) {
  try {
    const { filename, reparse = false } = await request.json();
    
    if (!filename) {
      return NextResponse.json(
        { success: false, error: '文件名不能为空' },
        { status: 400 }
      );
    }
    
    // 检查文件是否存在
    const availableFiles = getAvailableNovels();
    if (!availableFiles.includes(filename)) {
      return NextResponse.json(
        { success: false, error: '文件不存在' },
        { status: 404 }
      );
    }
    
    // 检查是否已经解析过
    if (!reparse && isNovelParsed(filename)) {
      return NextResponse.json(
        { success: false, error: '该小说已经解析过，如需重新解析请设置reparse=true' },
        { status: 409 }
      );
    }
    
    const novelsDir = path.join(process.cwd(), '..', 'novels');
    const filePath = path.join(novelsDir, filename);
    
    let result;
    if (reparse) {
      result = await reparseNovel(filename);
    } else {
      result = await parseNovelFile(filePath);
    }
    
    if (!result) {
      return NextResponse.json(
        { success: false, error: '解析失败' },
        { status: 500 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: result,
      message: `成功解析小说《${result.novel.title}》，共${result.chapters.length}章`,
    });
    
  } catch (error) {
    console.error('解析小说失败:', error);
    return NextResponse.json(
      { success: false, error: '解析小说失败' },
      { status: 500 }
    );
  }
}
