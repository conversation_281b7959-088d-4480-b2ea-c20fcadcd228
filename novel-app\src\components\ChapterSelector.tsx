'use client';

import { useState, useEffect } from 'react';
import { Novel, Chapter } from '@/lib/database';
import { FileText, Info } from 'lucide-react';

interface ChapterSelectorProps {
  novel: Novel | null;
  selectedChapters: string;
  onChaptersChange: (chapters: string) => void;
  disabled?: boolean;
}

export default function ChapterSelector({ 
  novel, 
  selectedChapters, 
  onChaptersChange, 
  disabled 
}: ChapterSelectorProps) {
  const [chapters, setChapters] = useState<Chapter[]>([]);
  const [loading, setLoading] = useState(false);
  const [previewChapters, setPreviewChapters] = useState<number[]>([]);

  useEffect(() => {
    if (novel) {
      loadChapters(novel.id);
    } else {
      setChapters([]);
      setPreviewChapters([]);
    }
  }, [novel]);

  useEffect(() => {
    // 解析章节范围并预览
    if (selectedChapters && chapters.length > 0) {
      const parsed = parseChapterRange(selectedChapters, chapters.length);
      setPreviewChapters(parsed);
    } else {
      setPreviewChapters([]);
    }
  }, [selectedChapters, chapters]);

  const loadChapters = async (novelId: string) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/chapters?novelId=${novelId}`);
      const result = await response.json();
      
      if (result.success) {
        setChapters(result.data);
      } else {
        console.error('加载章节列表失败:', result.error);
      }
    } catch (error) {
      console.error('加载章节列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const parseChapterRange = (rangeStr: string, maxChapter: number): number[] => {
    const chapters: number[] = [];
    const parts = rangeStr.split(',').map(part => part.trim());
    
    for (const part of parts) {
      if (part.includes('-')) {
        // 范围格式 (例如: "1-5")
        const [start, end] = part.split('-').map(num => parseInt(num.trim()));
        if (!isNaN(start) && !isNaN(end) && start <= end) {
          for (let i = start; i <= Math.min(end, maxChapter); i++) {
            if (i > 0 && !chapters.includes(i)) {
              chapters.push(i);
            }
          }
        }
      } else {
        // 单个章节
        const chapterNum = parseInt(part);
        if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {
          chapters.push(chapterNum);
        }
      }
    }
    
    return chapters.sort((a, b) => a - b);
  };

  const handleQuickSelect = (type: 'all' | 'first10' | 'last10') => {
    if (disabled || chapters.length === 0) return;

    let range = '';
    switch (type) {
      case 'all':
        range = `1-${chapters.length}`;
        break;
      case 'first10':
        range = `1-${Math.min(10, chapters.length)}`;
        break;
      case 'last10':
        range = `${Math.max(1, chapters.length - 9)}-${chapters.length}`;
        break;
    }
    onChaptersChange(range);
  };

  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
        <FileText className="mr-2" size={20} />
        选择章节
      </h2>

      {!novel ? (
        <div className="text-center py-8 text-gray-500">
          <FileText className="mx-auto mb-2" size={48} />
          <p>请先选择一部小说</p>
        </div>
      ) : loading ? (
        <div className="text-center py-8 text-gray-500">
          加载中...
        </div>
      ) : (
        <div className="space-y-4">
          {/* 章节范围输入 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              章节范围
            </label>
            <input
              type="text"
              value={selectedChapters}
              onChange={(e) => onChaptersChange(e.target.value)}
              disabled={disabled}
              placeholder="例如: 1-5,7,10-12"
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
            />
            <div className="mt-1 flex items-center text-xs text-gray-500">
              <Info className="mr-1" size={12} />
              支持范围(1-5)、单个章节(7)、组合(1-5,7,10-12)
            </div>
          </div>

          {/* 快速选择按钮 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              快速选择
            </label>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={() => handleQuickSelect('all')}
                disabled={disabled}
                className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50"
              >
                全部章节 (1-{chapters.length})
              </button>
              <button
                onClick={() => handleQuickSelect('first10')}
                disabled={disabled}
                className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50"
              >
                前10章
              </button>
              <button
                onClick={() => handleQuickSelect('last10')}
                disabled={disabled}
                className="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 rounded disabled:opacity-50"
              >
                后10章
              </button>
            </div>
          </div>

          {/* 章节预览 */}
          {previewChapters.length > 0 && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                将要改写的章节 ({previewChapters.length} 章)
              </label>
              <div className="max-h-40 overflow-y-auto border border-gray-200 rounded-md p-3 bg-gray-50">
                <div className="grid grid-cols-1 gap-1 text-sm">
                  {previewChapters.map((chapterNum) => {
                    const chapter = chapters.find(ch => ch.chapterNumber === chapterNum);
                    return (
                      <div key={chapterNum} className="flex items-center">
                        <span className="font-medium text-blue-600 w-12">
                          第{chapterNum}章
                        </span>
                        <span className="text-gray-700 truncate">
                          {chapter?.title || '未知标题'}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {/* 章节列表 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              所有章节 ({chapters.length} 章)
            </label>
            <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-md">
              {chapters.map((chapter) => (
                <div
                  key={chapter.id}
                  className={`p-2 border-b border-gray-100 last:border-b-0 ${
                    previewChapters.includes(chapter.chapterNumber)
                      ? 'bg-blue-50 border-l-4 border-l-blue-500'
                      : 'hover:bg-gray-50'
                  }`}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-gray-800 truncate">
                        第{chapter.chapterNumber}章 {chapter.title}
                      </div>
                      <div className="text-xs text-gray-500">
                        {chapter.content.length} 字符
                      </div>
                    </div>
                    {previewChapters.includes(chapter.chapterNumber) && (
                      <div className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                        已选择
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
