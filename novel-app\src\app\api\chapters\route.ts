import { NextRequest, NextResponse } from 'next/server';
import { chapterDb } from '@/lib/database';

// GET - 获取章节列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const novelId = searchParams.get('novelId');
    
    if (!novelId) {
      return NextResponse.json(
        { success: false, error: '小说ID不能为空' },
        { status: 400 }
      );
    }
    
    const chapters = chapterDb.getByNovelId(novelId);
    
    return NextResponse.json({
      success: true,
      data: chapters,
    });
  } catch (error) {
    console.error('获取章节列表失败:', error);
    return NextResponse.json(
      { success: false, error: '获取章节列表失败' },
      { status: 500 }
    );
  }
}
